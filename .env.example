# Required API Keys
ANTHROPIC_API_KEY="your-anthropic-api-key" # Needed if proxying *to* Anthropic
OPENAI_API_KEY="sk-..."
GEMINI_API_KEY="your-google-ai-studio-key"
XAI_API_KEY="your-xai-api-key" # Required if PREFERRED_PROVIDER=xai

# Vertex AI Configuration (Required if PREFERRED_PROVIDER=vertex)
# For Vertex AI, LiteLLM typically uses Application Default Credentials (ADC)
# Ensure ADC is set up (e.g., `gcloud auth application-default login`)
# Or set GOOGLE_APPLICATION_CREDENTIALS environment variable pointing to your service account key
VERTEX_PROJECT_ID="your-gcp-project-id" # Required for Vertex AI
VERTEX_LOCATION="us-central1" # Region where your Vertex AI resources are located

# Optional: Provider Preference and Model Mapping
# Controls which provider (openai, google, vertex, xai) is preferred for mapping haiku/sonnet.
# Defaults to openai if not set.
PREFERRED_PROVIDER="openai"

# Optional: Specify the exact models to map haiku/sonnet to.
# If PREFERRED_PROVIDER=google, these MUST be valid Gemini model names known to the server.
# If PREFERRED_PROVIDER=vertex, these MUST be valid Vertex AI model names known to the server.
# If PREFERRED_PROVIDER=xai, these MUST be valid xAI model names known to the server.
# Defaults to gemini-2.5-pro-preview-03-25 and gemini-2.0-flash if PREFERRED_PROVIDER=google or vertex.
# Defaults to gpt-4.1 and gpt-4.1-mini if PREFERRED_PROVIDER=openai.
# BIG_MODEL="gpt-4.1"
# SMALL_MODEL="gpt-4.1-mini"

# Example provider mappings:

# Google AI Studio mapping:
# PREFERRED_PROVIDER="google"
# BIG_MODEL="gemini-2.5-pro-preview-03-25"
# SMALL_MODEL="gemini-2.0-flash"

# Vertex AI mapping:
# PREFERRED_PROVIDER="vertex"
# BIG_MODEL="gemini-2.5-pro-preview-03-25"
# SMALL_MODEL="gemini-2.0-flash"
# VERTEX_PROJECT_ID="your-gcp-project-id"
# VERTEX_LOCATION="us-central1"

# xAI mapping:
# PREFERRED_PROVIDER="xai"
# BIG_MODEL="grok-3-beta"
# SMALL_MODEL="grok-3-mini-beta"
# XAI_API_KEY="your-xai-api-key"